"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FeedPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [needsSetup, setNeedsSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            checkUser();\n            fetchPosts();\n        }\n    }[\"FeedPage.useEffect\"], []);\n    const checkUser = async ()=>{\n        const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n        if (!user) {\n            router.push('/login');\n            return;\n        }\n        setUser(user);\n    };\n    const fetchPosts = async ()=>{\n        try {\n            // Simple approach - just get posts and handle users separately\n            const { data: posts, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('posts').select('*').order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching posts:', error);\n                setNeedsSetup(true);\n                setPosts([]);\n                return;\n            }\n            // If we have posts, try to get user data for each\n            if (posts && posts.length > 0) {\n                const postsWithUsers = await Promise.all(posts.map(async (post)=>{\n                    const { data: userData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username, profile_picture_url').eq('id', post.user_id).single();\n                    return {\n                        ...post,\n                        users: userData || {\n                            username: 'Anonymous',\n                            profile_picture_url: null\n                        }\n                    };\n                }));\n                setPosts(postsWithUsers);\n            } else {\n                setPosts([]);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setPosts([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLike = async (postId)=>{\n        if (!user) return;\n        try {\n            // Check if already liked\n            const { data: existingLike } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').select('id').eq('post_id', postId).eq('user_id', user.id).single();\n            if (existingLike) {\n                // Unlike\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').delete().eq('post_id', postId).eq('user_id', user.id);\n            } else {\n                // Like\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').insert({\n                    post_id: postId,\n                    user_id: user.id\n                });\n            }\n            // Refresh posts to get updated counts\n            fetchPosts();\n        } catch (error) {\n            console.error('Error toggling like:', error);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Just now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        if (diffInHours < 168) return \"\".concat(Math.floor(diffInHours / 24), \"d ago\");\n        return date.toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Monologue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/create'),\n                                    className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                    children: \"Write\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signOut().then(()=>router.push('/login')),\n                                    className: \"text-gray-600 hover:text-gray-900 text-sm\",\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto py-8 px-4\",\n                children: needsSetup ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-8 w-8 text-yellow-600 mr-3\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-yellow-800\",\n                                    children: \"Database Setup Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-700 mb-4\",\n                            children: \"The posts table hasn't been created yet. Please run the SQL setup commands in your Supabase dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-md p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 mb-2\",\n                                    children: \"Steps to fix:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"text-sm text-gray-700 space-y-1 list-decimal list-inside\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Go to your Supabase dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Navigate to SQL Editor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Run the SQL commands from SETUP.md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Refresh this page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this) : posts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-400\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No posts yet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Be the first to share your thoughts and poetry!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/create'),\n                            className: \"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors\",\n                            children: \"Write Your First Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: posts.map((post)=>{\n                        var _post_users, _post_users_username, _post_users1, _post_users2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: ((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.profile_picture_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                src: post.users.profile_picture_url,\n                                                alt: post.users.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-indigo-600 font-medium text-sm\",\n                                                    children: (_post_users1 = post.users) === null || _post_users1 === void 0 ? void 0 : (_post_users_username = _post_users1.username) === null || _post_users_username === void 0 ? void 0 : _post_users_username.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _post_users;\n                                                        return router.push(\"/profile/\".concat((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.username));\n                                                    },\n                                                    className: \"text-sm font-medium text-gray-900 hover:text-indigo-600 transition-colors\",\n                                                    children: ((_post_users2 = post.users) === null || _post_users2 === void 0 ? void 0 : _post_users2.username) || 'Anonymous'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: formatDate(post.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this),\n                                post.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-sm max-w-none mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-800 whitespace-pre-wrap leading-relaxed\",\n                                        children: post.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleLike(post.id),\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.likes_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.906-1.289L3 21l1.289-5.094A9.863 9.863 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.comments_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, post.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>router.push('/create'),\n                className: \"fixed bottom-6 right-6 bg-indigo-600 text-white p-4 rounded-full shadow-lg hover:bg-indigo-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 4v16m8-8H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mzyDZTPr6g63MJ0rSW5O29x/pgY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});