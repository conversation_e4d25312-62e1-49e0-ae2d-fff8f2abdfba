"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/signup/page",{

/***/ "(app-pages-browser)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SignUpPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        username: '',\n        agreeToTerms: false\n    });\n    const [profilePicture, setProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!emailRegex.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        // Password validation\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters long';\n        }\n        // Username validation\n        const usernameRegex = /^[a-zA-Z0-9_]+$/;\n        if (!formData.username) {\n            newErrors.username = 'Username is required';\n        } else if (formData.username.includes(' ')) {\n            newErrors.username = 'Username cannot contain spaces';\n        } else if (!usernameRegex.test(formData.username)) {\n            newErrors.username = 'Username can only contain letters, numbers, and underscores';\n        }\n        // Profile picture validation\n        if (profilePicture && profilePicture.size > 100 * 1024) {\n            newErrors.profilePicture = 'Profile picture must be less than 100 KB';\n        }\n        // Terms validation\n        if (!formData.agreeToTerms) {\n            newErrors.agreeToTerms = 'You must agree to the terms of service';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            setProfilePicture(file);\n            // Clear error when user selects a file\n            if (errors.profilePicture) {\n                setErrors((prev)=>({\n                        ...prev,\n                        profilePicture: ''\n                    }));\n            }\n        }\n    };\n    const uploadProfilePicture = async (userId)=>{\n        if (!profilePicture) return null;\n        try {\n            const fileExt = profilePicture.name.split('.').pop();\n            const fileName = \"\".concat(userId, \"-\").concat(Date.now(), \".\").concat(fileExt);\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').upload(fileName, profilePicture);\n            if (error) {\n                console.error('Error uploading profile picture:', error);\n                // Don't fail the entire signup for profile picture issues\n                return null;\n            }\n            const { data: { publicUrl } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').getPublicUrl(fileName);\n            return publicUrl;\n        } catch (error) {\n            console.error('Profile picture upload failed:', error);\n            return null;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        // Check if Supabase is configured\n        if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.isSupabaseConfigured)()) {\n            setErrors({\n                submit: 'Supabase is not configured. Please check the SETUP.md file for instructions.'\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Create user with Supabase Auth\n            const { data: authData, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password\n            });\n            if (authError) {\n                setErrors({\n                    submit: authError.message\n                });\n                return;\n            }\n            if (!authData.user) {\n                setErrors({\n                    submit: 'Failed to create user account'\n                });\n                return;\n            }\n            // Upload profile picture if provided\n            let profilePictureUrl = null;\n            if (profilePicture) {\n                profilePictureUrl = await uploadProfilePicture(authData.user.id);\n            }\n            // Save user profile data\n            const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').insert({\n                id: authData.user.id,\n                email: formData.email,\n                username: formData.username,\n                profile_picture_url: profilePictureUrl\n            });\n            if (profileError) {\n                setErrors({\n                    submit: 'Failed to save user profile'\n                });\n                return;\n            }\n            // Redirect to home feed\n            router.push('/feed');\n        } catch (error) {\n            setErrors({\n                submit: 'An unexpected error occurred'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-center text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"Monologue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-center text-xl text-gray-600\",\n                        children: \"Join the literary community\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    !(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.isSupabaseConfigured)() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: \"⚠️ Supabase not configured. Check SETUP.md for instructions.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Enter your email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Minimum 8 characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.password\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"username\",\n                                                    name: \"username\",\n                                                    type: \"text\",\n                                                    autoComplete: \"username\",\n                                                    required: true,\n                                                    value: formData.username,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"No spaces allowed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"profilePicture\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Profile Picture (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"profilePicture\",\n                                                    name: \"profilePicture\",\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleFileChange,\n                                                    className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"Maximum file size: 100 KB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.profilePicture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.profilePicture\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"agreeToTerms\",\n                                            name: \"agreeToTerms\",\n                                            type: \"checkbox\",\n                                            checked: formData.agreeToTerms,\n                                            onChange: handleInputChange,\n                                            className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"agreeToTerms\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: [\n                                                \"I agree to the\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    className: \"text-indigo-600 hover:text-indigo-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                errors.agreeToTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600\",\n                                    children: errors.agreeToTerms\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isLoading ? 'Creating Account...' : 'Create Account'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-600 text-center\",\n                                    children: errors.submit\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Already have an account?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/login\",\n                                            className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPage, \"otWvRBMupQQOVIfIpvOK9imQzRA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SignUpPage;\nvar _c;\n$RefreshReg$(_c, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/signup/page.tsx\n"));

/***/ })

});