/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/_not-found/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/_not-found/page\",\n      function () {\n        return __webpack_require__(/*! ./node_modules/next/dist/client/components/not-found-error.js */ \"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/_not-found/page\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPUMlM0ElNUNVc2VycyU1Q2hwJTVDRGVza3RvcCU1Q01vbm9sb2d1ZSU1Q21vbm9sb2d1ZSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q25vdC1mb3VuZC1lcnJvci5qcyZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLHdKQUErRDtBQUN0RjtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9fbm90LWZvdW5kL3BhZ2VcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yLmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi9fbm90LWZvdW5kL3BhZ2VcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessErrorFallback\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessErrorFallback;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst styles = {\n    error: {\n        // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n        fontFamily: 'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n        height: '100vh',\n        textAlign: 'center',\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center'\n    },\n    desc: {\n        display: 'inline-block'\n    },\n    h1: {\n        display: 'inline-block',\n        margin: '0 20px 0 0',\n        padding: '0 23px 0 0',\n        fontSize: 24,\n        fontWeight: 500,\n        verticalAlign: 'top',\n        lineHeight: '49px'\n    },\n    h2: {\n        fontSize: 14,\n        fontWeight: 400,\n        lineHeight: '49px',\n        margin: 0\n    }\n};\nfunction HTTPAccessErrorFallback(param) {\n    let { status, message } = param;\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"title\", {\n                children: status + \": \" + message\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                style: styles.error,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"style\", {\n                            dangerouslySetInnerHTML: {\n                                /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */ __html: \"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"\n                            }\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h1\", {\n                            className: \"next-error-h1\",\n                            style: styles.h1,\n                            children: status\n                        }),\n                        /*#__PURE__*/ (0, _jsxruntime.jsx)(\"div\", {\n                            style: styles.desc,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"h2\", {\n                                style: styles.h2,\n                                children: message\n                            })\n                        })\n                    ]\n                })\n            })\n        ]\n    });\n}\n_c = HTTPAccessErrorFallback;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-fallback.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessErrorFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItZmFsbGJhY2suanMiLCJtYXBwaW5ncyI6Ijs7OzsyREFxQ2dCQTs7O2VBQUFBOzs7Ozs0RUFyQ0U7QUFFbEIsTUFBTUMsU0FBOEM7SUFDbERDLE9BQU87UUFDTCwwRkFBMEY7UUFDMUZDLFlBQ0U7UUFDRkMsUUFBUTtRQUNSQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsZUFBZTtRQUNmQyxZQUFZO1FBQ1pDLGdCQUFnQjtJQUNsQjtJQUVBQyxNQUFNO1FBQ0pKLFNBQVM7SUFDWDtJQUVBSyxJQUFJO1FBQ0ZMLFNBQVM7UUFDVE0sUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLFlBQVk7SUFDZDtJQUVBQyxJQUFJO1FBQ0ZKLFVBQVU7UUFDVkMsWUFBWTtRQUNaRSxZQUFZO1FBQ1pMLFFBQVE7SUFDVjtBQUNGO0FBRU8saUNBQWlDLEtBTXZDO0lBTnVDLE1BQ3RDTyxNQUFNLEVBQ05DLE9BQU8sRUFJUixHQU51QztJQU90QyxxQkFDRTs7MEJBRUUscUJBQUNDLFNBQUFBOzBCQUFVRixTQUFPLE9BQUlDOzswQkFFdEIscUJBQUNFLE9BQUFBO2dCQUFJQyxPQUFPdEIsT0FBT0MsS0FBSzswQkFDdEIsb0NBQUNvQixPQUFBQTs7c0NBQ0MscUJBQUNDLFNBQUFBOzRCQUNDQyx5QkFBeUI7Z0NBQ3ZCOzs7Ozs7Ozs7Ozs7Y0FZQSxHQUNBQyxRQUFTOzRCQUNYOztzQ0FFRixxQkFBQ2QsTUFBQUE7NEJBQUdlLFdBQVU7NEJBQWdCSCxPQUFPdEIsT0FBT1UsRUFBRTtzQ0FDM0NROztzQ0FFSCxxQkFBQ0csT0FBQUE7NEJBQUlDLE9BQU90QixPQUFPUyxJQUFJO3NDQUNyQixtQ0FBQ1EsTUFBQUE7Z0NBQUdLLE9BQU90QixPQUFPaUIsRUFBRTswQ0FBR0U7Ozs7Ozs7O0FBTW5DO0tBMUNnQnBCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxodHRwLWFjY2Vzcy1mYWxsYmFja1xcZXJyb3ItZmFsbGJhY2sudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcblxuY29uc3Qgc3R5bGVzOiBSZWNvcmQ8c3RyaW5nLCBSZWFjdC5DU1NQcm9wZXJ0aWVzPiA9IHtcbiAgZXJyb3I6IHtcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vc2luZHJlc29yaHVzL21vZGVybi1ub3JtYWxpemUvYmxvYi9tYWluL21vZGVybi1ub3JtYWxpemUuY3NzI0wzOC1MNTJcbiAgICBmb250RmFtaWx5OlxuICAgICAgJ3N5c3RlbS11aSxcIlNlZ29lIFVJXCIsUm9ib3RvLEhlbHZldGljYSxBcmlhbCxzYW5zLXNlcmlmLFwiQXBwbGUgQ29sb3IgRW1vamlcIixcIlNlZ29lIFVJIEVtb2ppXCInLFxuICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gIH0sXG5cbiAgZGVzYzoge1xuICAgIGRpc3BsYXk6ICdpbmxpbmUtYmxvY2snLFxuICB9LFxuXG4gIGgxOiB7XG4gICAgZGlzcGxheTogJ2lubGluZS1ibG9jaycsXG4gICAgbWFyZ2luOiAnMCAyMHB4IDAgMCcsXG4gICAgcGFkZGluZzogJzAgMjNweCAwIDAnLFxuICAgIGZvbnRTaXplOiAyNCxcbiAgICBmb250V2VpZ2h0OiA1MDAsXG4gICAgdmVydGljYWxBbGlnbjogJ3RvcCcsXG4gICAgbGluZUhlaWdodDogJzQ5cHgnLFxuICB9LFxuXG4gIGgyOiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGZvbnRXZWlnaHQ6IDQwMCxcbiAgICBsaW5lSGVpZ2h0OiAnNDlweCcsXG4gICAgbWFyZ2luOiAwLFxuICB9LFxufVxuXG5leHBvcnQgZnVuY3Rpb24gSFRUUEFjY2Vzc0Vycm9yRmFsbGJhY2soe1xuICBzdGF0dXMsXG4gIG1lc3NhZ2UsXG59OiB7XG4gIHN0YXR1czogbnVtYmVyXG4gIG1lc3NhZ2U6IHN0cmluZ1xufSkge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7LyogPGhlYWQ+ICovfVxuICAgICAgPHRpdGxlPntgJHtzdGF0dXN9OiAke21lc3NhZ2V9YH08L3RpdGxlPlxuICAgICAgey8qIDwvaGVhZD4gKi99XG4gICAgICA8ZGl2IHN0eWxlPXtzdHlsZXMuZXJyb3J9PlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxzdHlsZVxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgICAgICAgLyogTWluaWZpZWQgQ1NTIGZyb21cbiAgICAgICAgICAgICAgICBib2R5IHsgbWFyZ2luOiAwOyBjb2xvcjogIzAwMDsgYmFja2dyb3VuZDogI2ZmZjsgfVxuICAgICAgICAgICAgICAgIC5uZXh0LWVycm9yLWgxIHtcbiAgICAgICAgICAgICAgICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgLjMpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIEBtZWRpYSAocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspIHtcbiAgICAgICAgICAgICAgICAgIGJvZHkgeyBjb2xvcjogI2ZmZjsgYmFja2dyb3VuZDogIzAwMDsgfVxuICAgICAgICAgICAgICAgICAgLm5leHQtZXJyb3ItaDEge1xuICAgICAgICAgICAgICAgICAgICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIC4zKTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICovXG4gICAgICAgICAgICAgIF9faHRtbDogYGJvZHl7Y29sb3I6IzAwMDtiYWNrZ3JvdW5kOiNmZmY7bWFyZ2luOjB9Lm5leHQtZXJyb3ItaDF7Ym9yZGVyLXJpZ2h0OjFweCBzb2xpZCByZ2JhKDAsMCwwLC4zKX1AbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOmRhcmspe2JvZHl7Y29sb3I6I2ZmZjtiYWNrZ3JvdW5kOiMwMDB9Lm5leHQtZXJyb3ItaDF7Ym9yZGVyLXJpZ2h0OjFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LC4zKX19YCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwibmV4dC1lcnJvci1oMVwiIHN0eWxlPXtzdHlsZXMuaDF9PlxuICAgICAgICAgICAge3N0YXR1c31cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3N0eWxlcy5kZXNjfT5cbiAgICAgICAgICAgIDxoMiBzdHlsZT17c3R5bGVzLmgyfT57bWVzc2FnZX08L2gyPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsiSFRUUEFjY2Vzc0Vycm9yRmFsbGJhY2siLCJzdHlsZXMiLCJlcnJvciIsImZvbnRGYW1pbHkiLCJoZWlnaHQiLCJ0ZXh0QWxpZ24iLCJkaXNwbGF5IiwiZmxleERpcmVjdGlvbiIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImRlc2MiLCJoMSIsIm1hcmdpbiIsInBhZGRpbmciLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJ2ZXJ0aWNhbEFsaWduIiwibGluZUhlaWdodCIsImgyIiwic3RhdHVzIiwibWVzc2FnZSIsInRpdGxlIiwiZGl2Iiwic3R5bGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js":
/*!*********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/not-found-error.js ***!
  \*********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return NotFound;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _errorfallback = __webpack_require__(/*! ./http-access-fallback/error-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-fallback.js\");\nfunction NotFound() {\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorfallback.HTTPAccessErrorFallback, {\n        status: 404,\n        message: \"This page could not be found.\"\n    });\n}\n_c = NotFound;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=not-found-error.js.map\nvar _c;\n$RefreshReg$(_c, \"NotFound\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBRUE7OztlQUF3QkE7Ozs7MkNBRmdCO0FBRXpCO0lBQ2IscUJBQ0UscUJBQUNDLGVBQUFBLHVCQUF1QjtRQUN0QkMsUUFBUTtRQUNSQyxTQUFROztBQUdkO0tBUHdCSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcc3JjXFxjbGllbnRcXGNvbXBvbmVudHNcXG5vdC1mb3VuZC1lcnJvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSFRUUEFjY2Vzc0Vycm9yRmFsbGJhY2sgfSBmcm9tICcuL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWZhbGxiYWNrJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SFRUUEFjY2Vzc0Vycm9yRmFsbGJhY2tcbiAgICAgIHN0YXR1cz17NDA0fVxuICAgICAgbWVzc2FnZT1cIlRoaXMgcGFnZSBjb3VsZCBub3QgYmUgZm91bmQuXCJcbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiTm90Rm91bmQiLCJIVFRQQWNjZXNzRXJyb3JGYWxsYmFjayIsInN0YXR1cyIsIm1lc3NhZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-error.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-error.js&page=%2F_not-found%2Fpage!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);