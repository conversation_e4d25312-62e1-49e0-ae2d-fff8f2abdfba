"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FeedPage() {\n    var _userProfile_username;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [needsSetup, setNeedsSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            checkUser();\n            fetchPosts();\n        }\n    }[\"FeedPage.useEffect\"], []);\n    const checkUser = async ()=>{\n        const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n        if (!user) {\n            router.push('/login');\n            return;\n        }\n        setUser(user);\n        // Get user profile data\n        const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username, profile_picture_url').eq('id', user.id).single();\n        setUserProfile(profile);\n    };\n    const fetchPosts = async ()=>{\n        try {\n            // Simple approach - just get posts and handle users separately\n            const { data: posts, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('posts').select('*').order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching posts:', error);\n                setNeedsSetup(true);\n                setPosts([]);\n                return;\n            }\n            // If we have posts, try to get user data for each\n            if (posts && posts.length > 0) {\n                const postsWithUsers = await Promise.all(posts.map(async (post)=>{\n                    const { data: userData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username, profile_picture_url').eq('id', post.user_id).single();\n                    return {\n                        ...post,\n                        users: userData || {\n                            username: 'Anonymous',\n                            profile_picture_url: null\n                        }\n                    };\n                }));\n                setPosts(postsWithUsers);\n            } else {\n                setPosts([]);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setPosts([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLike = async (postId)=>{\n        if (!user) return;\n        try {\n            // Check if already liked\n            const { data: existingLike } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').select('id').eq('post_id', postId).eq('user_id', user.id).single();\n            if (existingLike) {\n                // Unlike\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').delete().eq('post_id', postId).eq('user_id', user.id);\n            } else {\n                // Like\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').insert({\n                    post_id: postId,\n                    user_id: user.id\n                });\n            }\n            // Refresh posts to get updated counts\n            fetchPosts();\n        } catch (error) {\n            console.error('Error toggling like:', error);\n        }\n    };\n    const toggleComments = async (postId)=>{\n        const isShowing = showComments[postId];\n        if (!isShowing) {\n            // Load comments for this post\n            const { data: commentsData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('comments').select(\"\\n          *,\\n          users (\\n            username,\\n            profile_picture_url\\n          )\\n        \").eq('post_id', postId).order('created_at', {\n                ascending: true\n            });\n            setComments((prev)=>({\n                    ...prev,\n                    [postId]: commentsData || []\n                }));\n        }\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !isShowing\n            }));\n    };\n    const handleCommentSubmit = async (postId)=>{\n        var _newComment_postId;\n        const commentText = (_newComment_postId = newComment[postId]) === null || _newComment_postId === void 0 ? void 0 : _newComment_postId.trim();\n        if (!commentText || !user) return;\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('comments').insert({\n                post_id: postId,\n                user_id: user.id,\n                content: commentText\n            });\n            if (error) {\n                console.error('Error adding comment:', error);\n                return;\n            }\n            // Clear the input\n            setNewComment((prev)=>({\n                    ...prev,\n                    [postId]: ''\n                }));\n            // Reload comments and posts to update counts\n            await toggleComments(postId);\n            fetchPosts();\n        } catch (error) {\n            console.error('Error:', error);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Just now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        if (diffInHours < 168) return \"\".concat(Math.floor(diffInHours / 24), \"d ago\");\n        return date.toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Monologue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/create'),\n                                    className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                    children: \"Write\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                userProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/profile/\".concat(userProfile.username)),\n                                    className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        userProfile.profile_picture_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            className: \"h-8 w-8 rounded-full object-cover\",\n                                            src: userProfile.profile_picture_url,\n                                            alt: userProfile.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-indigo-600 font-medium text-xs\",\n                                                children: (_userProfile_username = userProfile.username) === null || _userProfile_username === void 0 ? void 0 : _userProfile_username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: userProfile.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signOut().then(()=>router.push('/login')),\n                                    className: \"text-gray-600 hover:text-gray-900 text-sm\",\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto py-8 px-4\",\n                children: needsSetup ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-8 w-8 text-yellow-600 mr-3\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-yellow-800\",\n                                    children: \"Database Setup Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-700 mb-4\",\n                            children: \"The posts table hasn't been created yet. Please run the SQL setup commands in your Supabase dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-md p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 mb-2\",\n                                    children: \"Steps to fix:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"text-sm text-gray-700 space-y-1 list-decimal list-inside\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Go to your Supabase dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Navigate to SQL Editor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Run the SQL commands from SETUP.md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Refresh this page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this) : posts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-400\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No posts yet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Be the first to share your thoughts and poetry!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/create'),\n                            className: \"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors\",\n                            children: \"Write Your First Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: posts.map((post)=>{\n                        var _post_users, _post_users_username, _post_users1, _post_users2, _userProfile_username, _newComment_post_id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: ((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.profile_picture_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                src: post.users.profile_picture_url,\n                                                alt: post.users.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-indigo-600 font-medium text-sm\",\n                                                    children: (_post_users1 = post.users) === null || _post_users1 === void 0 ? void 0 : (_post_users_username = _post_users1.username) === null || _post_users_username === void 0 ? void 0 : _post_users_username.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _post_users;\n                                                        return router.push(\"/profile/\".concat((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.username));\n                                                    },\n                                                    className: \"text-sm font-medium text-gray-900 hover:text-indigo-600 transition-colors\",\n                                                    children: ((_post_users2 = post.users) === null || _post_users2 === void 0 ? void 0 : _post_users2.username) || 'Anonymous'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: formatDate(post.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 17\n                                }, this),\n                                post.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-sm max-w-none mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-800 whitespace-pre-wrap leading-relaxed\",\n                                        children: post.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleLike(post.id),\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.likes_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleComments(post.id),\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.906-1.289L3 21l1.289-5.094A9.863 9.863 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.comments_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 17\n                                }, this),\n                                showComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        comments[post.id] && comments[post.id].length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 mb-4\",\n                                            children: comments[post.id].map((comment)=>{\n                                                var _comment_users, _comment_users_username, _comment_users1, _comment_users2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: ((_comment_users = comment.users) === null || _comment_users === void 0 ? void 0 : _comment_users.profile_picture_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                className: \"h-8 w-8 rounded-full object-cover\",\n                                                                src: comment.users.profile_picture_url,\n                                                                alt: comment.users.username\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 33\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-indigo-600 font-medium text-xs\",\n                                                                    children: (_comment_users1 = comment.users) === null || _comment_users1 === void 0 ? void 0 : (_comment_users_username = _comment_users1.username) === null || _comment_users_username === void 0 ? void 0 : _comment_users_username.charAt(0).toUpperCase()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-50 rounded-lg px-3 py-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                var _comment_users;\n                                                                                return router.push(\"/profile/\".concat((_comment_users = comment.users) === null || _comment_users === void 0 ? void 0 : _comment_users.username));\n                                                                            },\n                                                                            className: \"text-sm font-medium text-gray-900 hover:text-indigo-600\",\n                                                                            children: (_comment_users2 = comment.users) === null || _comment_users2 === void 0 ? void 0 : _comment_users2.username\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-800 mt-1\",\n                                                                            children: comment.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-1 ml-3\",\n                                                                    children: formatDate(comment.created_at)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, comment.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 27\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.profile_picture_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-8 w-8 rounded-full object-cover\",\n                                                        src: userProfile.profile_picture_url,\n                                                        alt: userProfile.username\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-indigo-600 font-medium text-xs\",\n                                                            children: userProfile === null || userProfile === void 0 ? void 0 : (_userProfile_username = userProfile.username) === null || _userProfile_username === void 0 ? void 0 : _userProfile_username.charAt(0).toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: newComment[post.id] || '',\n                                                                onChange: (e)=>setNewComment((prev)=>({\n                                                                            ...prev,\n                                                                            [post.id]: e.target.value\n                                                                        })),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleCommentSubmit(post.id),\n                                                                placeholder: \"Write a comment...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleCommentSubmit(post.id),\n                                                                disabled: !((_newComment_post_id = newComment[post.id]) === null || _newComment_post_id === void 0 ? void 0 : _newComment_post_id.trim()),\n                                                                className: \"px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: \"Post\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, post.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>router.push('/create'),\n                className: \"fixed bottom-6 right-6 bg-indigo-600 text-white p-4 rounded-full shadow-lg hover:bg-indigo-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 4v16m8-8H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"eentRroxlWr25lOzxjqJaZ0ZF+0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});