/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/signup/page";
exports.ids = ["app/signup/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/signup/page.tsx */ \"(rsc)/./src/app/signup/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'signup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/signup/page\",\n        pathname: \"/signup\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/signup/page.tsx */ \"(rsc)/./src/app/signup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q01vbm9sb2d1ZSU1QyU1Q21vbm9sb2d1ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3NpZ251cCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBNkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhwXFxcXERlc2t0b3BcXFxcTW9ub2xvZ3VlXFxcXG1vbm9sb2d1ZVxcXFxzcmNcXFxcYXBwXFxcXHNpZ251cFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXE1vbm9sb2d1ZVxcbW9ub2xvZ3VlXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxNb25vbG9ndWVcXG1vbm9sb2d1ZVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXE1vbm9sb2d1ZVxcbW9ub2xvZ3VlXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Monologue\\monologue\\src\\app\\signup\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/signup/page.tsx */ \"(ssr)/./src/app/signup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hwJTVDJTVDRGVza3RvcCU1QyU1Q01vbm9sb2d1ZSU1QyU1Q21vbm9sb2d1ZSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3NpZ251cCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBNkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGhwXFxcXERlc2t0b3BcXFxcTW9ub2xvZ3VlXFxcXG1vbm9sb2d1ZVxcXFxzcmNcXFxcYXBwXFxcXHNpZ251cFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CMonologue%5C%5Cmonologue%5C%5Csrc%5C%5Capp%5C%5Csignup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction SignUpPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        username: '',\n        agreeToTerms: false\n    });\n    const [profilePicture, setProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!emailRegex.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        // Password validation\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters long';\n        }\n        // Username validation\n        const usernameRegex = /^[a-zA-Z0-9_]+$/;\n        if (!formData.username) {\n            newErrors.username = 'Username is required';\n        } else if (formData.username.includes(' ')) {\n            newErrors.username = 'Username cannot contain spaces';\n        } else if (!usernameRegex.test(formData.username)) {\n            newErrors.username = 'Username can only contain letters, numbers, and underscores';\n        }\n        // Profile picture validation\n        if (profilePicture && profilePicture.size > 100 * 1024) {\n            newErrors.profilePicture = 'Profile picture must be less than 100 KB';\n        }\n        // Terms validation\n        if (!formData.agreeToTerms) {\n            newErrors.agreeToTerms = 'You must agree to the terms of service';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        const file = e.target.files?.[0];\n        if (file) {\n            setProfilePicture(file);\n            // Clear error when user selects a file\n            if (errors.profilePicture) {\n                setErrors((prev)=>({\n                        ...prev,\n                        profilePicture: ''\n                    }));\n            }\n        }\n    };\n    const uploadProfilePicture = async (userId)=>{\n        if (!profilePicture) return null;\n        try {\n            const fileExt = profilePicture.name.split('.').pop();\n            const fileName = `${userId}-${Date.now()}.${fileExt}`;\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').upload(fileName, profilePicture);\n            if (error) {\n                console.error('Error uploading profile picture:', error);\n                // Don't fail the entire signup for profile picture issues\n                return null;\n            }\n            const { data: { publicUrl } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').getPublicUrl(fileName);\n            return publicUrl;\n        } catch (error) {\n            console.error('Profile picture upload failed:', error);\n            return null;\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        // Check if Supabase is configured\n        if (!(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.isSupabaseConfigured)()) {\n            setErrors({\n                submit: 'Supabase is not configured. Please check the SETUP.md file for instructions.'\n            });\n            return;\n        }\n        setIsLoading(true);\n        try {\n            // Check if username already exists\n            const { data: existingUser } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username').eq('username', formData.username).single();\n            if (existingUser) {\n                setErrors({\n                    username: 'Username already taken'\n                });\n                return;\n            }\n            // Create user with Supabase Auth\n            const { data: authData, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password\n            });\n            if (authError) {\n                setErrors({\n                    submit: authError.message\n                });\n                return;\n            }\n            if (!authData.user) {\n                setErrors({\n                    submit: 'Failed to create user account'\n                });\n                return;\n            }\n            // Check if email confirmation is required\n            if (!authData.session && authData.user && !authData.user.email_confirmed_at) {\n                // Email confirmation required\n                setErrors({\n                    submit: 'Please check your email and click the confirmation link to complete signup. Then try signing in.'\n                });\n                return;\n            }\n            // Upload profile picture if provided (don't fail signup if this fails)\n            let profilePictureUrl = null;\n            if (profilePicture) {\n                profilePictureUrl = await uploadProfilePicture(authData.user.id);\n            }\n            // Save user profile data with retry logic\n            let retryCount = 0;\n            let profileError = null;\n            while(retryCount < 3){\n                const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').insert({\n                    id: authData.user.id,\n                    email: formData.email,\n                    username: formData.username,\n                    profile_picture_url: profilePictureUrl\n                });\n                if (!error) {\n                    // Success! Redirect to home feed\n                    router.push('/feed');\n                    return;\n                }\n                profileError = error;\n                retryCount++;\n                // Wait a bit before retrying\n                if (retryCount < 3) {\n                    await new Promise((resolve)=>setTimeout(resolve, 1000));\n                }\n            }\n            // If we get here, all retries failed\n            console.error('Profile save error:', profileError);\n            setErrors({\n                submit: `Failed to save user profile: ${profileError?.message || 'Unknown error'}. Please try again.`\n            });\n        } catch (error) {\n            console.error('Signup error:', error);\n            setErrors({\n                submit: 'An unexpected error occurred. Please try again.'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-center text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"Monologue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-center text-xl text-gray-600\",\n                        children: \"Join the literary community\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    !(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.isSupabaseConfigured)() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: \"⚠️ Supabase not configured. Check SETUP.md for instructions.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Enter your email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Minimum 8 characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.password\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"username\",\n                                                    name: \"username\",\n                                                    type: \"text\",\n                                                    autoComplete: \"username\",\n                                                    required: true,\n                                                    value: formData.username,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"No spaces allowed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"profilePicture\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Profile Picture (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"profilePicture\",\n                                                    name: \"profilePicture\",\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleFileChange,\n                                                    className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"Maximum file size: 100 KB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.profilePicture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.profilePicture\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"agreeToTerms\",\n                                            name: \"agreeToTerms\",\n                                            type: \"checkbox\",\n                                            checked: formData.agreeToTerms,\n                                            onChange: handleInputChange,\n                                            className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"agreeToTerms\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: [\n                                                \"I agree to the\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    className: \"text-indigo-600 hover:text-indigo-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                errors.agreeToTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600\",\n                                    children: errors.agreeToTerms\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isLoading ? 'Creating Account...' : 'Create Account'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-600 text-center\",\n                                    children: errors.submit\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Already have an account?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/login\",\n                                            className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/signup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSupabaseConfigured: () => (/* binding */ isSupabaseConfigured),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://wnbkflcbrkbxfdeqodga.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InduYmtmbGNicmtieGZkZXFvZGdhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0NzA1NjMsImV4cCI6MjA2NzA0NjU2M30.-IbLO39pqdOm947XfDOT231b5uEsaiEfyroCXk-nXAg\" || 0;\n// Check if Supabase is properly configured\nconst isSupabaseConfigured = ()=>{\n    return supabaseUrl !== 'https://placeholder.supabase.co' && supabaseAnonKey !== 'placeholder-key' && supabaseUrl.includes('supabase.co');\n};\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?7009":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?eb14":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/@swc","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fsignup%2Fpage&page=%2Fsignup%2Fpage&appPaths=%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fsignup%2Fpage.tsx&appDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chp%5CDesktop%5CMonologue%5Cmonologue&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();