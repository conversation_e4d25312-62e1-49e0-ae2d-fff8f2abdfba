'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'

export default function ConfirmEmailPage() {
  const [email, setEmail] = useState('')
  const [isResending, setIsResending] = useState(false)
  const [message, setMessage] = useState('')

  const handleResendConfirmation = async () => {
    if (!email) {
      setMessage('Please enter your email address')
      return
    }

    setIsResending(true)
    setMessage('')

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      })

      if (error) {
        setMessage(`Error: ${error.message}`)
      } else {
        setMessage('Confirmation email sent! Please check your inbox.')
      }
    } catch (err) {
      setMessage('Failed to resend confirmation email')
    } finally {
      setIsResending(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h1 className="text-center text-3xl font-bold text-gray-900 mb-2">
          Monologue
        </h1>
        <h2 className="text-center text-xl text-gray-600 mb-8">
          Check Your Email
        </h2>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center mb-6">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Account Created Successfully!
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              We've sent a confirmation email to your inbox. Please click the link in the email to verify your account and complete the signup process.
            </p>
            <p className="text-xs text-gray-500">
              Don't forget to check your spam folder if you don't see the email.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Didn't receive the email? Enter your email to resend:
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Enter your email"
              />
            </div>

            <button
              onClick={handleResendConfirmation}
              disabled={isResending}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? 'Sending...' : 'Resend Confirmation Email'}
            </button>

            {message && (
              <div className={`text-sm text-center ${message.includes('Error') ? 'text-red-600' : 'text-green-600'}`}>
                {message}
              </div>
            )}
          </div>

          <div className="mt-6 text-center space-y-2">
            <p className="text-sm text-gray-600">
              Already confirmed your email?
            </p>
            <a 
              href="/login" 
              className="font-medium text-indigo-600 hover:text-indigo-500"
            >
              Sign in to your account
            </a>
          </div>

          <div className="mt-4 text-center">
            <a 
              href="/signup" 
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              ← Back to Sign Up
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
