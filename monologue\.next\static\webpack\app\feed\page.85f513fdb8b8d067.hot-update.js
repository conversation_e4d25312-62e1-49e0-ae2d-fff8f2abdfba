"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FeedPage() {\n    var _userProfile_username;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [needsSetup, setNeedsSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            checkUser();\n            fetchPosts();\n        }\n    }[\"FeedPage.useEffect\"], []);\n    const checkUser = async ()=>{\n        const { data: { user } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.getUser();\n        if (!user) {\n            router.push('/login');\n            return;\n        }\n        setUser(user);\n        // Get user profile data\n        const { data: profile } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username, profile_picture_url').eq('id', user.id).single();\n        setUserProfile(profile);\n    };\n    const fetchPosts = async ()=>{\n        try {\n            // Simple approach - just get posts and handle users separately\n            const { data: posts, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('posts').select('*').order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching posts:', error);\n                setNeedsSetup(true);\n                setPosts([]);\n                return;\n            }\n            // If we have posts, try to get user data for each\n            if (posts && posts.length > 0) {\n                const postsWithUsers = await Promise.all(posts.map(async (post)=>{\n                    const { data: userData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').select('username, profile_picture_url').eq('id', post.user_id).single();\n                    return {\n                        ...post,\n                        users: userData || {\n                            username: 'Anonymous',\n                            profile_picture_url: null\n                        }\n                    };\n                }));\n                setPosts(postsWithUsers);\n            } else {\n                setPosts([]);\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setPosts([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLike = async (postId)=>{\n        if (!user) return;\n        try {\n            // Check if already liked\n            const { data: existingLike } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').select('id').eq('post_id', postId).eq('user_id', user.id).single();\n            if (existingLike) {\n                // Unlike\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').delete().eq('post_id', postId).eq('user_id', user.id);\n            } else {\n                // Like\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('likes').insert({\n                    post_id: postId,\n                    user_id: user.id\n                });\n            }\n            // Refresh posts to get updated counts\n            fetchPosts();\n        } catch (error) {\n            console.error('Error toggling like:', error);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n        if (diffInHours < 1) return 'Just now';\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n        if (diffInHours < 168) return \"\".concat(Math.floor(diffInHours / 24), \"d ago\");\n        return date.toLocaleDateString();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Monologue\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/create'),\n                                    className: \"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors\",\n                                    children: \"Write\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                userProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(\"/profile/\".concat(userProfile.username)),\n                                    className: \"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\",\n                                    children: [\n                                        userProfile.profile_picture_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            className: \"h-8 w-8 rounded-full object-cover\",\n                                            src: userProfile.profile_picture_url,\n                                            alt: userProfile.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-indigo-600 font-medium text-xs\",\n                                                children: (_userProfile_username = userProfile.username) === null || _userProfile_username === void 0 ? void 0 : _userProfile_username.charAt(0).toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: userProfile.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>_lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signOut().then(()=>router.push('/login')),\n                                    className: \"text-gray-600 hover:text-gray-900 text-sm\",\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto py-8 px-4\",\n                children: needsSetup ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-8 w-8 text-yellow-600 mr-3\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-yellow-800\",\n                                    children: \"Database Setup Required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-700 mb-4\",\n                            children: \"The posts table hasn't been created yet. Please run the SQL setup commands in your Supabase dashboard.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-md p-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900 mb-2\",\n                                    children: \"Steps to fix:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                    className: \"text-sm text-gray-700 space-y-1 list-decimal list-inside\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Go to your Supabase dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Navigate to SQL Editor\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Run the SQL commands from SETUP.md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Refresh this page\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700\",\n                            children: \"Refresh Page\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this) : posts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-400\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No posts yet\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Be the first to share your thoughts and poetry!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/create'),\n                            className: \"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors\",\n                            children: \"Write Your First Post\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: posts.map((post)=>{\n                        var _post_users, _post_users_username, _post_users1, _post_users2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: ((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.profile_picture_url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                className: \"h-10 w-10 rounded-full object-cover\",\n                                                src: post.users.profile_picture_url,\n                                                alt: post.users.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-indigo-600 font-medium text-sm\",\n                                                    children: (_post_users1 = post.users) === null || _post_users1 === void 0 ? void 0 : (_post_users_username = _post_users1.username) === null || _post_users_username === void 0 ? void 0 : _post_users_username.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _post_users;\n                                                        return router.push(\"/profile/\".concat((_post_users = post.users) === null || _post_users === void 0 ? void 0 : _post_users.username));\n                                                    },\n                                                    className: \"text-sm font-medium text-gray-900 hover:text-indigo-600 transition-colors\",\n                                                    children: ((_post_users2 = post.users) === null || _post_users2 === void 0 ? void 0 : _post_users2.username) || 'Anonymous'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: formatDate(post.created_at)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 17\n                                }, this),\n                                post.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: post.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-sm max-w-none mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-800 whitespace-pre-wrap leading-relaxed\",\n                                        children: post.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 pt-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleLike(post.id),\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.likes_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center space-x-2 text-gray-500 hover:text-blue-500 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a9.863 9.863 0 01-4.906-1.289L3 21l1.289-5.094A9.863 9.863 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: post.comments_count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, post.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>router.push('/create'),\n                className: \"fixed bottom-6 right-6 bg-indigo-600 text-white p-4 rounded-full shadow-lg hover:bg-indigo-700 transition-colors\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-6 w-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: \"2\",\n                        d: \"M12 4v16m8-8H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mzyDZTPr6g63MJ0rSW5O29x/pgY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});