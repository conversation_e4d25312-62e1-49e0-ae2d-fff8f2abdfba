# Monologue - Setup Instructions

## Supabase Configuration

To complete the setup, you need to configure Supabase:

### 1. Create a Supabase Project
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Copy your project URL and anon key

### 2. Update Environment Variables
Update the `.env.local` file with your Supabase credentials:
```
NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
```

### 3. Create Database Tables
Run these SQL commands in your Supabase SQL editor:

```sql
-- Create users table
CREATE TABLE users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  username TEXT UNIQUE NOT NULL,
  profile_picture_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create storage bucket for profile pictures
INSERT INTO storage.buckets (id, name, public) VALUES ('profile-pictures', 'profile-pictures', true);

-- Create policy for profile pictures
CREATE POLICY "Users can upload their own profile pictures" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'profile-pictures' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Profile pictures are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'profile-pictures');

-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY "Users can read their own data" ON users
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own data" ON users
FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own data" ON users
FOR UPDATE USING (auth.uid() = id);
```

### 4. Install Dependencies
Make sure Supabase is installed:
```bash
npm install @supabase/supabase-js
```

### 5. Run the Development Server
```bash
npm run dev
```

## Features Implemented

### Sign Up Page (`/signup`)
- Email validation
- Password validation (minimum 8 characters)
- Username validation (no spaces, unique)
- Optional profile picture upload (100 KB limit)
- Terms of service checkbox
- Supabase Auth integration
- Profile data storage in users table
- Redirect to feed after successful signup

### Navigation
- Landing page with links to signup/login
- Placeholder login page
- Placeholder feed page

## Next Steps
1. Set up your Supabase project and update environment variables
2. Run the database setup SQL
3. Test the signup flow
4. Implement additional pages (login, feed, etc.)
