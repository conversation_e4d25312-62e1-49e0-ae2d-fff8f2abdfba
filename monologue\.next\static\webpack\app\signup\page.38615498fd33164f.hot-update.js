"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/signup/page",{

/***/ "(app-pages-browser)/./src/app/signup/page.tsx":
/*!*********************************!*\
  !*** ./src/app/signup/page.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SignUpPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        username: '',\n        agreeToTerms: false\n    });\n    const [profilePicture, setProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!emailRegex.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        // Password validation\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters long';\n        }\n        // Username validation\n        const usernameRegex = /^[a-zA-Z0-9_]+$/;\n        if (!formData.username) {\n            newErrors.username = 'Username is required';\n        } else if (formData.username.includes(' ')) {\n            newErrors.username = 'Username cannot contain spaces';\n        } else if (!usernameRegex.test(formData.username)) {\n            newErrors.username = 'Username can only contain letters, numbers, and underscores';\n        }\n        // Profile picture validation\n        if (profilePicture && profilePicture.size > 100 * 1024) {\n            newErrors.profilePicture = 'Profile picture must be less than 100 KB';\n        }\n        // Terms validation\n        if (!formData.agreeToTerms) {\n            newErrors.agreeToTerms = 'You must agree to the terms of service';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n        // Clear error when user starts typing\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: ''\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            setProfilePicture(file);\n            // Clear error when user selects a file\n            if (errors.profilePicture) {\n                setErrors((prev)=>({\n                        ...prev,\n                        profilePicture: ''\n                    }));\n            }\n        }\n    };\n    const uploadProfilePicture = async (userId)=>{\n        if (!profilePicture) return null;\n        const fileExt = profilePicture.name.split('.').pop();\n        const fileName = \"\".concat(userId, \"-\").concat(Date.now(), \".\").concat(fileExt);\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').upload(fileName, profilePicture);\n        if (error) {\n            console.error('Error uploading profile picture:', error);\n            return null;\n        }\n        const { data: { publicUrl } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.storage.from('profile-pictures').getPublicUrl(fileName);\n        return publicUrl;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            // Create user with Supabase Auth\n            const { data: authData, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.auth.signUp({\n                email: formData.email,\n                password: formData.password\n            });\n            if (authError) {\n                setErrors({\n                    submit: authError.message\n                });\n                return;\n            }\n            if (!authData.user) {\n                setErrors({\n                    submit: 'Failed to create user account'\n                });\n                return;\n            }\n            // Upload profile picture if provided\n            let profilePictureUrl = null;\n            if (profilePicture) {\n                profilePictureUrl = await uploadProfilePicture(authData.user.id);\n            }\n            // Save user profile data\n            const { error: profileError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('users').insert({\n                id: authData.user.id,\n                email: formData.email,\n                username: formData.username,\n                profile_picture_url: profilePictureUrl\n            });\n            if (profileError) {\n                setErrors({\n                    submit: 'Failed to save user profile'\n                });\n                return;\n            }\n            // Redirect to home feed\n            router.push('/feed');\n        } catch (error) {\n            setErrors({\n                submit: 'An unexpected error occurred'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-center text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"Monologue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-center text-xl text-gray-600\",\n                        children: \"Join the literary community\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Enter your email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: \"password\",\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"Minimum 8 characters\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.password\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"username\",\n                                                    name: \"username\",\n                                                    type: \"text\",\n                                                    autoComplete: \"username\",\n                                                    required: true,\n                                                    value: formData.username,\n                                                    onChange: handleInputChange,\n                                                    className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                                    placeholder: \"No spaces allowed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"profilePicture\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Profile Picture (Optional)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"profilePicture\",\n                                                    name: \"profilePicture\",\n                                                    type: \"file\",\n                                                    accept: \"image/*\",\n                                                    onChange: handleFileChange,\n                                                    className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500\",\n                                                    children: \"Maximum file size: 100 KB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.profilePicture && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.profilePicture\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"agreeToTerms\",\n                                            name: \"agreeToTerms\",\n                                            type: \"checkbox\",\n                                            checked: formData.agreeToTerms,\n                                            onChange: handleInputChange,\n                                            className: \"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"agreeToTerms\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: [\n                                                \"I agree to the\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/terms\",\n                                                    className: \"text-indigo-600 hover:text-indigo-500\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                errors.agreeToTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-600\",\n                                    children: errors.agreeToTerms\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isLoading ? 'Creating Account...' : 'Create Account'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-600 text-center\",\n                                    children: errors.submit\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"Already have an account?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/login\",\n                                            className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                            children: \"Sign in\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Monologue\\\\monologue\\\\src\\\\app\\\\signup\\\\page.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPage, \"otWvRBMupQQOVIfIpvOK9imQzRA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SignUpPage;\nvar _c;\n$RefreshReg$(_c, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvc2lnbnVwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ1c7QUFDb0I7QUFFaEQsU0FBU0c7O0lBQ3RCLE1BQU1DLFNBQVNILDBEQUFTQTtJQUN4QixNQUFNLENBQUNJLFVBQVVDLFlBQVksR0FBR04sK0NBQVFBLENBQUM7UUFDdkNPLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGNBQWM7SUFDaEI7SUFDQSxNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdaLCtDQUFRQSxDQUFjO0lBQ2xFLE1BQU0sQ0FBQ2EsUUFBUUMsVUFBVSxHQUFHZCwrQ0FBUUEsQ0FBeUIsQ0FBQztJQUM5RCxNQUFNLENBQUNlLFdBQVdDLGFBQWEsR0FBR2hCLCtDQUFRQSxDQUFDO0lBRTNDLE1BQU1pQixlQUFlO1FBQ25CLE1BQU1DLFlBQW9DLENBQUM7UUFFM0MsbUJBQW1CO1FBQ25CLE1BQU1DLGFBQWE7UUFDbkIsSUFBSSxDQUFDZCxTQUFTRSxLQUFLLEVBQUU7WUFDbkJXLFVBQVVYLEtBQUssR0FBRztRQUNwQixPQUFPLElBQUksQ0FBQ1ksV0FBV0MsSUFBSSxDQUFDZixTQUFTRSxLQUFLLEdBQUc7WUFDM0NXLFVBQVVYLEtBQUssR0FBRztRQUNwQjtRQUVBLHNCQUFzQjtRQUN0QixJQUFJLENBQUNGLFNBQVNHLFFBQVEsRUFBRTtZQUN0QlUsVUFBVVYsUUFBUSxHQUFHO1FBQ3ZCLE9BQU8sSUFBSUgsU0FBU0csUUFBUSxDQUFDYSxNQUFNLEdBQUcsR0FBRztZQUN2Q0gsVUFBVVYsUUFBUSxHQUFHO1FBQ3ZCO1FBRUEsc0JBQXNCO1FBQ3RCLE1BQU1jLGdCQUFnQjtRQUN0QixJQUFJLENBQUNqQixTQUFTSSxRQUFRLEVBQUU7WUFDdEJTLFVBQVVULFFBQVEsR0FBRztRQUN2QixPQUFPLElBQUlKLFNBQVNJLFFBQVEsQ0FBQ2MsUUFBUSxDQUFDLE1BQU07WUFDMUNMLFVBQVVULFFBQVEsR0FBRztRQUN2QixPQUFPLElBQUksQ0FBQ2EsY0FBY0YsSUFBSSxDQUFDZixTQUFTSSxRQUFRLEdBQUc7WUFDakRTLFVBQVVULFFBQVEsR0FBRztRQUN2QjtRQUVBLDZCQUE2QjtRQUM3QixJQUFJRSxrQkFBa0JBLGVBQWVhLElBQUksR0FBRyxNQUFNLE1BQU07WUFDdEROLFVBQVVQLGNBQWMsR0FBRztRQUM3QjtRQUVBLG1CQUFtQjtRQUNuQixJQUFJLENBQUNOLFNBQVNLLFlBQVksRUFBRTtZQUMxQlEsVUFBVVIsWUFBWSxHQUFHO1FBQzNCO1FBRUFJLFVBQVVJO1FBQ1YsT0FBT08sT0FBT0MsSUFBSSxDQUFDUixXQUFXRyxNQUFNLEtBQUs7SUFDM0M7SUFFQSxNQUFNTSxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsSUFBSSxFQUFFQyxPQUFPLEVBQUUsR0FBR0osRUFBRUssTUFBTTtRQUMvQzNCLFlBQVk0QixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQLENBQUNMLEtBQUssRUFBRUUsU0FBUyxhQUFhQyxVQUFVRjtZQUMxQztRQUVBLHNDQUFzQztRQUN0QyxJQUFJakIsTUFBTSxDQUFDZ0IsS0FBSyxFQUFFO1lBQ2hCZixVQUFVb0IsQ0FBQUEsT0FBUztvQkFBRSxHQUFHQSxJQUFJO29CQUFFLENBQUNMLEtBQUssRUFBRTtnQkFBRztRQUMzQztJQUNGO0lBRUEsTUFBTU0sbUJBQW1CLENBQUNQO1lBQ1hBO1FBQWIsTUFBTVEsUUFBT1Isa0JBQUFBLEVBQUVLLE1BQU0sQ0FBQ0ksS0FBSyxjQUFkVCxzQ0FBQUEsZUFBZ0IsQ0FBQyxFQUFFO1FBQ2hDLElBQUlRLE1BQU07WUFDUnhCLGtCQUFrQndCO1lBQ2xCLHVDQUF1QztZQUN2QyxJQUFJdkIsT0FBT0YsY0FBYyxFQUFFO2dCQUN6QkcsVUFBVW9CLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRXZCLGdCQUFnQjtvQkFBRztZQUNuRDtRQUNGO0lBQ0Y7SUFFQSxNQUFNMkIsdUJBQXVCLE9BQU9DO1FBQ2xDLElBQUksQ0FBQzVCLGdCQUFnQixPQUFPO1FBRTVCLE1BQU02QixVQUFVN0IsZUFBZWtCLElBQUksQ0FBQ1ksS0FBSyxDQUFDLEtBQUtDLEdBQUc7UUFDbEQsTUFBTUMsV0FBVyxHQUFhQyxPQUFWTCxRQUFPLEtBQWlCQyxPQUFkSSxLQUFLQyxHQUFHLElBQUcsS0FBVyxPQUFSTDtRQUU1QyxNQUFNLEVBQUVNLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTTdDLG1EQUFRQSxDQUFDOEMsT0FBTyxDQUMzQ0MsSUFBSSxDQUFDLG9CQUNMQyxNQUFNLENBQUNQLFVBQVVoQztRQUVwQixJQUFJb0MsT0FBTztZQUNUSSxRQUFRSixLQUFLLENBQUMsb0NBQW9DQTtZQUNsRCxPQUFPO1FBQ1Q7UUFFQSxNQUFNLEVBQUVELE1BQU0sRUFBRU0sU0FBUyxFQUFFLEVBQUUsR0FBR2xELG1EQUFRQSxDQUFDOEMsT0FBTyxDQUM3Q0MsSUFBSSxDQUFDLG9CQUNMSSxZQUFZLENBQUNWO1FBRWhCLE9BQU9TO0lBQ1Q7SUFFQSxNQUFNRSxlQUFlLE9BQU8xQjtRQUMxQkEsRUFBRTJCLGNBQWM7UUFFaEIsSUFBSSxDQUFDdEMsZ0JBQWdCO1FBRXJCRCxhQUFhO1FBRWIsSUFBSTtZQUNGLGlDQUFpQztZQUNqQyxNQUFNLEVBQUU4QixNQUFNVSxRQUFRLEVBQUVULE9BQU9VLFNBQVMsRUFBRSxHQUFHLE1BQU12RCxtREFBUUEsQ0FBQ3dELElBQUksQ0FBQ0MsTUFBTSxDQUFDO2dCQUN0RXBELE9BQU9GLFNBQVNFLEtBQUs7Z0JBQ3JCQyxVQUFVSCxTQUFTRyxRQUFRO1lBQzdCO1lBRUEsSUFBSWlELFdBQVc7Z0JBQ2IzQyxVQUFVO29CQUFFOEMsUUFBUUgsVUFBVUksT0FBTztnQkFBQztnQkFDdEM7WUFDRjtZQUVBLElBQUksQ0FBQ0wsU0FBU00sSUFBSSxFQUFFO2dCQUNsQmhELFVBQVU7b0JBQUU4QyxRQUFRO2dCQUFnQztnQkFDcEQ7WUFDRjtZQUVBLHFDQUFxQztZQUNyQyxJQUFJRyxvQkFBb0I7WUFDeEIsSUFBSXBELGdCQUFnQjtnQkFDbEJvRCxvQkFBb0IsTUFBTXpCLHFCQUFxQmtCLFNBQVNNLElBQUksQ0FBQ0UsRUFBRTtZQUNqRTtZQUVBLHlCQUF5QjtZQUN6QixNQUFNLEVBQUVqQixPQUFPa0IsWUFBWSxFQUFFLEdBQUcsTUFBTS9ELG1EQUFRQSxDQUMzQytDLElBQUksQ0FBQyxTQUNMaUIsTUFBTSxDQUFDO2dCQUNORixJQUFJUixTQUFTTSxJQUFJLENBQUNFLEVBQUU7Z0JBQ3BCekQsT0FBT0YsU0FBU0UsS0FBSztnQkFDckJFLFVBQVVKLFNBQVNJLFFBQVE7Z0JBQzNCMEQscUJBQXFCSjtZQUN2QjtZQUVGLElBQUlFLGNBQWM7Z0JBQ2hCbkQsVUFBVTtvQkFBRThDLFFBQVE7Z0JBQThCO2dCQUNsRDtZQUNGO1lBRUEsd0JBQXdCO1lBQ3hCeEQsT0FBT2dFLElBQUksQ0FBQztRQUNkLEVBQUUsT0FBT3JCLE9BQU87WUFDZGpDLFVBQVU7Z0JBQUU4QyxRQUFRO1lBQStCO1FBQ3JELFNBQVU7WUFDUjVDLGFBQWE7UUFDZjtJQUNGO0lBRUEscUJBQ0UsOERBQUNxRDtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBb0Q7Ozs7OztrQ0FHbEUsOERBQUNFO3dCQUFHRixXQUFVO2tDQUFvQzs7Ozs7Ozs7Ozs7OzBCQUtwRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0c7NEJBQUtILFdBQVU7NEJBQVlJLFVBQVVwQjs7OENBRXBDLDhEQUFDZTs7c0RBQ0MsOERBQUNNOzRDQUFNQyxTQUFROzRDQUFRTixXQUFVO3NEQUEwQzs7Ozs7O3NEQUczRSw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTztvREFDQ2IsSUFBRztvREFDSG5DLE1BQUs7b0RBQ0xFLE1BQUs7b0RBQ0wrQyxjQUFhO29EQUNiQyxRQUFRO29EQUNSakQsT0FBT3pCLFNBQVNFLEtBQUs7b0RBQ3JCeUUsVUFBVXJEO29EQUNWMkMsV0FBVTtvREFDVlcsYUFBWTs7Ozs7O2dEQUVicEUsT0FBT04sS0FBSyxrQkFDWCw4REFBQzJFO29EQUFFWixXQUFVOzhEQUE2QnpELE9BQU9OLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNNUQsOERBQUM4RDs7c0RBQ0MsOERBQUNNOzRDQUFNQyxTQUFROzRDQUFXTixXQUFVO3NEQUEwQzs7Ozs7O3NEQUc5RSw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTztvREFDQ2IsSUFBRztvREFDSG5DLE1BQUs7b0RBQ0xFLE1BQUs7b0RBQ0wrQyxjQUFhO29EQUNiQyxRQUFRO29EQUNSakQsT0FBT3pCLFNBQVNHLFFBQVE7b0RBQ3hCd0UsVUFBVXJEO29EQUNWMkMsV0FBVTtvREFDVlcsYUFBWTs7Ozs7O2dEQUVicEUsT0FBT0wsUUFBUSxrQkFDZCw4REFBQzBFO29EQUFFWixXQUFVOzhEQUE2QnpELE9BQU9MLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNL0QsOERBQUM2RDs7c0RBQ0MsOERBQUNNOzRDQUFNQyxTQUFROzRDQUFXTixXQUFVO3NEQUEwQzs7Ozs7O3NEQUc5RSw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDTztvREFDQ2IsSUFBRztvREFDSG5DLE1BQUs7b0RBQ0xFLE1BQUs7b0RBQ0wrQyxjQUFhO29EQUNiQyxRQUFRO29EQUNSakQsT0FBT3pCLFNBQVNJLFFBQVE7b0RBQ3hCdUUsVUFBVXJEO29EQUNWMkMsV0FBVTtvREFDVlcsYUFBWTs7Ozs7O2dEQUVicEUsT0FBT0osUUFBUSxrQkFDZCw4REFBQ3lFO29EQUFFWixXQUFVOzhEQUE2QnpELE9BQU9KLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNL0QsOERBQUM0RDs7c0RBQ0MsOERBQUNNOzRDQUFNQyxTQUFROzRDQUFpQk4sV0FBVTtzREFBMEM7Ozs7OztzREFHcEYsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ087b0RBQ0NiLElBQUc7b0RBQ0huQyxNQUFLO29EQUNMRSxNQUFLO29EQUNMb0QsUUFBTztvREFDUEgsVUFBVTdDO29EQUNWbUMsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDWTtvREFBRVosV0FBVTs4REFBNkI7Ozs7OztnREFDekN6RCxPQUFPRixjQUFjLGtCQUNwQiw4REFBQ3VFO29EQUFFWixXQUFVOzhEQUE2QnpELE9BQU9GLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNckUsOERBQUMwRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNPOzRDQUNDYixJQUFHOzRDQUNIbkMsTUFBSzs0Q0FDTEUsTUFBSzs0Q0FDTEMsU0FBUzNCLFNBQVNLLFlBQVk7NENBQzlCc0UsVUFBVXJEOzRDQUNWMkMsV0FBVTs7Ozs7O3NEQUVaLDhEQUFDSzs0Q0FBTUMsU0FBUTs0Q0FBZU4sV0FBVTs7Z0RBQW1DO2dEQUMxRDs4REFDZiw4REFBQ2M7b0RBQUVDLE1BQUs7b0RBQVNmLFdBQVU7OERBQXdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBS3RFekQsT0FBT0gsWUFBWSxrQkFDbEIsOERBQUN3RTtvQ0FBRVosV0FBVTs4Q0FBd0J6RCxPQUFPSCxZQUFZOzs7Ozs7OENBSTFELDhEQUFDMkQ7OENBQ0MsNEVBQUNpQjt3Q0FDQ3ZELE1BQUs7d0NBQ0x3RCxVQUFVeEU7d0NBQ1Z1RCxXQUFVO2tEQUVUdkQsWUFBWSx3QkFBd0I7Ozs7Ozs7Ozs7O2dDQUt4Q0YsT0FBTytDLE1BQU0sa0JBQ1osOERBQUNTO29DQUFJQyxXQUFVOzhDQUNaekQsT0FBTytDLE1BQU07Ozs7Ozs7Ozs7OztzQ0FLcEIsOERBQUNTOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2tCO29DQUFLbEIsV0FBVTs7d0NBQXdCO3dDQUNiO3NEQUN6Qiw4REFBQ2M7NENBQUVDLE1BQUs7NENBQVNmLFdBQVU7c0RBQW9EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVL0Y7R0F4VHdCbkU7O1FBQ1BGLHNEQUFTQTs7O0tBREZFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxNb25vbG9ndWVcXG1vbm9sb2d1ZVxcc3JjXFxhcHBcXHNpZ251cFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyBzdXBhYmFzZSwgaXNTdXBhYmFzZUNvbmZpZ3VyZWQgfSBmcm9tICdAL2xpYi9zdXBhYmFzZSdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2lnblVwUGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgZW1haWw6ICcnLFxuICAgIHBhc3N3b3JkOiAnJyxcbiAgICB1c2VybmFtZTogJycsXG4gICAgYWdyZWVUb1Rlcm1zOiBmYWxzZVxuICB9KVxuICBjb25zdCBbcHJvZmlsZVBpY3R1cmUsIHNldFByb2ZpbGVQaWN0dXJlXSA9IHVzZVN0YXRlPEZpbGUgfCBudWxsPihudWxsKVxuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge31cblxuICAgIC8vIEVtYWlsIHZhbGlkYXRpb25cbiAgICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC9cbiAgICBpZiAoIWZvcm1EYXRhLmVtYWlsKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAnRW1haWwgaXMgcmVxdWlyZWQnXG4gICAgfSBlbHNlIGlmICghZW1haWxSZWdleC50ZXN0KGZvcm1EYXRhLmVtYWlsKSkge1xuICAgICAgbmV3RXJyb3JzLmVtYWlsID0gJ1BsZWFzZSBlbnRlciBhIHZhbGlkIGVtYWlsIGFkZHJlc3MnXG4gICAgfVxuXG4gICAgLy8gUGFzc3dvcmQgdmFsaWRhdGlvblxuICAgIGlmICghZm9ybURhdGEucGFzc3dvcmQpIHtcbiAgICAgIG5ld0Vycm9ycy5wYXNzd29yZCA9ICdQYXNzd29yZCBpcyByZXF1aXJlZCdcbiAgICB9IGVsc2UgaWYgKGZvcm1EYXRhLnBhc3N3b3JkLmxlbmd0aCA8IDgpIHtcbiAgICAgIG5ld0Vycm9ycy5wYXNzd29yZCA9ICdQYXNzd29yZCBtdXN0IGJlIGF0IGxlYXN0IDggY2hhcmFjdGVycyBsb25nJ1xuICAgIH1cblxuICAgIC8vIFVzZXJuYW1lIHZhbGlkYXRpb25cbiAgICBjb25zdCB1c2VybmFtZVJlZ2V4ID0gL15bYS16QS1aMC05X10rJC9cbiAgICBpZiAoIWZvcm1EYXRhLnVzZXJuYW1lKSB7XG4gICAgICBuZXdFcnJvcnMudXNlcm5hbWUgPSAnVXNlcm5hbWUgaXMgcmVxdWlyZWQnXG4gICAgfSBlbHNlIGlmIChmb3JtRGF0YS51c2VybmFtZS5pbmNsdWRlcygnICcpKSB7XG4gICAgICBuZXdFcnJvcnMudXNlcm5hbWUgPSAnVXNlcm5hbWUgY2Fubm90IGNvbnRhaW4gc3BhY2VzJ1xuICAgIH0gZWxzZSBpZiAoIXVzZXJuYW1lUmVnZXgudGVzdChmb3JtRGF0YS51c2VybmFtZSkpIHtcbiAgICAgIG5ld0Vycm9ycy51c2VybmFtZSA9ICdVc2VybmFtZSBjYW4gb25seSBjb250YWluIGxldHRlcnMsIG51bWJlcnMsIGFuZCB1bmRlcnNjb3JlcydcbiAgICB9XG5cbiAgICAvLyBQcm9maWxlIHBpY3R1cmUgdmFsaWRhdGlvblxuICAgIGlmIChwcm9maWxlUGljdHVyZSAmJiBwcm9maWxlUGljdHVyZS5zaXplID4gMTAwICogMTAyNCkge1xuICAgICAgbmV3RXJyb3JzLnByb2ZpbGVQaWN0dXJlID0gJ1Byb2ZpbGUgcGljdHVyZSBtdXN0IGJlIGxlc3MgdGhhbiAxMDAgS0InXG4gICAgfVxuXG4gICAgLy8gVGVybXMgdmFsaWRhdGlvblxuICAgIGlmICghZm9ybURhdGEuYWdyZWVUb1Rlcm1zKSB7XG4gICAgICBuZXdFcnJvcnMuYWdyZWVUb1Rlcm1zID0gJ1lvdSBtdXN0IGFncmVlIHRvIHRoZSB0ZXJtcyBvZiBzZXJ2aWNlJ1xuICAgIH1cblxuICAgIHNldEVycm9ycyhuZXdFcnJvcnMpXG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwXG4gIH1cblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChlOiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IHsgbmFtZSwgdmFsdWUsIHR5cGUsIGNoZWNrZWQgfSA9IGUudGFyZ2V0XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtuYW1lXTogdHlwZSA9PT0gJ2NoZWNrYm94JyA/IGNoZWNrZWQgOiB2YWx1ZVxuICAgIH0pKVxuICAgIFxuICAgIC8vIENsZWFyIGVycm9yIHdoZW4gdXNlciBzdGFydHMgdHlwaW5nXG4gICAgaWYgKGVycm9yc1tuYW1lXSkge1xuICAgICAgc2V0RXJyb3JzKHByZXYgPT4gKHsgLi4ucHJldiwgW25hbWVdOiAnJyB9KSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVGaWxlQ2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGUudGFyZ2V0LmZpbGVzPy5bMF1cbiAgICBpZiAoZmlsZSkge1xuICAgICAgc2V0UHJvZmlsZVBpY3R1cmUoZmlsZSlcbiAgICAgIC8vIENsZWFyIGVycm9yIHdoZW4gdXNlciBzZWxlY3RzIGEgZmlsZVxuICAgICAgaWYgKGVycm9ycy5wcm9maWxlUGljdHVyZSkge1xuICAgICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBwcm9maWxlUGljdHVyZTogJycgfSkpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgY29uc3QgdXBsb2FkUHJvZmlsZVBpY3R1cmUgPSBhc3luYyAodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZyB8IG51bGw+ID0+IHtcbiAgICBpZiAoIXByb2ZpbGVQaWN0dXJlKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgZmlsZUV4dCA9IHByb2ZpbGVQaWN0dXJlLm5hbWUuc3BsaXQoJy4nKS5wb3AoKVxuICAgIGNvbnN0IGZpbGVOYW1lID0gYCR7dXNlcklkfS0ke0RhdGUubm93KCl9LiR7ZmlsZUV4dH1gXG4gICAgXG4gICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2Uuc3RvcmFnZVxuICAgICAgLmZyb20oJ3Byb2ZpbGUtcGljdHVyZXMnKVxuICAgICAgLnVwbG9hZChmaWxlTmFtZSwgcHJvZmlsZVBpY3R1cmUpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwbG9hZGluZyBwcm9maWxlIHBpY3R1cmU6JywgZXJyb3IpXG4gICAgICByZXR1cm4gbnVsbFxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YTogeyBwdWJsaWNVcmwgfSB9ID0gc3VwYWJhc2Uuc3RvcmFnZVxuICAgICAgLmZyb20oJ3Byb2ZpbGUtcGljdHVyZXMnKVxuICAgICAgLmdldFB1YmxpY1VybChmaWxlTmFtZSlcblxuICAgIHJldHVybiBwdWJsaWNVcmxcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBcbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSByZXR1cm5cblxuICAgIHNldElzTG9hZGluZyh0cnVlKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENyZWF0ZSB1c2VyIHdpdGggU3VwYWJhc2UgQXV0aFxuICAgICAgY29uc3QgeyBkYXRhOiBhdXRoRGF0YSwgZXJyb3I6IGF1dGhFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduVXAoe1xuICAgICAgICBlbWFpbDogZm9ybURhdGEuZW1haWwsXG4gICAgICAgIHBhc3N3b3JkOiBmb3JtRGF0YS5wYXNzd29yZCxcbiAgICAgIH0pXG5cbiAgICAgIGlmIChhdXRoRXJyb3IpIHtcbiAgICAgICAgc2V0RXJyb3JzKHsgc3VibWl0OiBhdXRoRXJyb3IubWVzc2FnZSB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgaWYgKCFhdXRoRGF0YS51c2VyKSB7XG4gICAgICAgIHNldEVycm9ycyh7IHN1Ym1pdDogJ0ZhaWxlZCB0byBjcmVhdGUgdXNlciBhY2NvdW50JyB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gVXBsb2FkIHByb2ZpbGUgcGljdHVyZSBpZiBwcm92aWRlZFxuICAgICAgbGV0IHByb2ZpbGVQaWN0dXJlVXJsID0gbnVsbFxuICAgICAgaWYgKHByb2ZpbGVQaWN0dXJlKSB7XG4gICAgICAgIHByb2ZpbGVQaWN0dXJlVXJsID0gYXdhaXQgdXBsb2FkUHJvZmlsZVBpY3R1cmUoYXV0aERhdGEudXNlci5pZClcbiAgICAgIH1cblxuICAgICAgLy8gU2F2ZSB1c2VyIHByb2ZpbGUgZGF0YVxuICAgICAgY29uc3QgeyBlcnJvcjogcHJvZmlsZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcnMnKVxuICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICBpZDogYXV0aERhdGEudXNlci5pZCxcbiAgICAgICAgICBlbWFpbDogZm9ybURhdGEuZW1haWwsXG4gICAgICAgICAgdXNlcm5hbWU6IGZvcm1EYXRhLnVzZXJuYW1lLFxuICAgICAgICAgIHByb2ZpbGVfcGljdHVyZV91cmw6IHByb2ZpbGVQaWN0dXJlVXJsLFxuICAgICAgICB9KVxuXG4gICAgICBpZiAocHJvZmlsZUVycm9yKSB7XG4gICAgICAgIHNldEVycm9ycyh7IHN1Ym1pdDogJ0ZhaWxlZCB0byBzYXZlIHVzZXIgcHJvZmlsZScgfSlcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIC8vIFJlZGlyZWN0IHRvIGhvbWUgZmVlZFxuICAgICAgcm91dGVyLnB1c2goJy9mZWVkJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgc2V0RXJyb3JzKHsgc3VibWl0OiAnQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGZsZXgtY29sIGp1c3RpZnktY2VudGVyIHB5LTEyIHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzbTpteC1hdXRvIHNtOnctZnVsbCBzbTptYXgtdy1tZFwiPlxuICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgIE1vbm9sb2d1ZVxuICAgICAgICA8L2gxPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC14bCB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgSm9pbiB0aGUgbGl0ZXJhcnkgY29tbXVuaXR5XG4gICAgICAgIDwvaDI+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHNtOm14LWF1dG8gc206dy1mdWxsIHNtOm1heC13LW1kXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcHktOCBweC00IHNoYWRvdyBzbTpyb3VuZGVkLWxnIHNtOnB4LTEwXCI+XG4gICAgICAgICAgPGZvcm0gY2xhc3NOYW1lPVwic3BhY2UteS02XCIgb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0+XG4gICAgICAgICAgICB7LyogRW1haWwgSW5wdXQgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImVtYWlsXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgRW1haWwgYWRkcmVzc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTFcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmVtYWlsfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYXBwZWFyYW5jZS1ub25lIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHBsYWNlaG9sZGVyLWdyYXktNDAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWluZGlnby01MDAgZm9jdXM6Ym9yZGVyLWluZGlnby01MDAgc206dGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgZW1haWxcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5lbWFpbCAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5lbWFpbH08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFBhc3N3b3JkIElucHV0ICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJwYXNzd29yZFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgIFBhc3N3b3JkXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMVwiPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICBuYW1lPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cIm5ldy1wYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYXBwZWFyYW5jZS1ub25lIGJsb2NrIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHBsYWNlaG9sZGVyLWdyYXktNDAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLWluZGlnby01MDAgZm9jdXM6Ym9yZGVyLWluZGlnby01MDAgc206dGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIk1pbmltdW0gOCBjaGFyYWN0ZXJzXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMucGFzc3dvcmQgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucGFzc3dvcmR9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBVc2VybmFtZSBJbnB1dCAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidXNlcm5hbWVcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICBVc2VybmFtZVxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTFcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwidXNlcm5hbWVcIlxuICAgICAgICAgICAgICAgICAgbmFtZT1cInVzZXJuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIGF1dG9Db21wbGV0ZT1cInVzZXJuYW1lXCJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudXNlcm5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhcHBlYXJhbmNlLW5vbmUgYmxvY2sgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctaW5kaWdvLTUwMCBmb2N1czpib3JkZXItaW5kaWdvLTUwMCBzbTp0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiTm8gc3BhY2VzIGFsbG93ZWRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy51c2VybmFtZSAmJiAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy51c2VybmFtZX08L3A+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFByb2ZpbGUgUGljdHVyZSBVcGxvYWQgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInByb2ZpbGVQaWN0dXJlXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgUHJvZmlsZSBQaWN0dXJlIChPcHRpb25hbClcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cInByb2ZpbGVQaWN0dXJlXCJcbiAgICAgICAgICAgICAgICAgIG5hbWU9XCJwcm9maWxlUGljdHVyZVwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS8qXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVGaWxlQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHRleHQtc20gdGV4dC1ncmF5LTUwMCBmaWxlOm1yLTQgZmlsZTpweS0yIGZpbGU6cHgtNCBmaWxlOnJvdW5kZWQtbWQgZmlsZTpib3JkZXItMCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOmJnLWluZGlnby01MCBmaWxlOnRleHQtaW5kaWdvLTcwMCBob3ZlcjpmaWxlOmJnLWluZGlnby0xMDBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5NYXhpbXVtIGZpbGUgc2l6ZTogMTAwIEtCPC9wPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMucHJvZmlsZVBpY3R1cmUgJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntlcnJvcnMucHJvZmlsZVBpY3R1cmV9PC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUZXJtcyBDaGVja2JveCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJhZ3JlZVRvVGVybXNcIlxuICAgICAgICAgICAgICAgIG5hbWU9XCJhZ3JlZVRvVGVybXNcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEuYWdyZWVUb1Rlcm1zfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtaW5kaWdvLTYwMCBmb2N1czpyaW5nLWluZGlnby01MDAgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImFncmVlVG9UZXJtc1wiIGNsYXNzTmFtZT1cIm1sLTIgYmxvY2sgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgSSBhZ3JlZSB0byB0aGV7JyAnfVxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWluZGlnby02MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwXCI+XG4gICAgICAgICAgICAgICAgICBUZXJtcyBvZiBTZXJ2aWNlXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICB7ZXJyb3JzLmFncmVlVG9UZXJtcyAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5hZ3JlZVRvVGVybXN9PC9wPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIFN1Ym1pdCBCdXR0b24gKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgZmxleCBqdXN0aWZ5LWNlbnRlciBweS0yIHB4LTQgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctaW5kaWdvLTYwMCBob3ZlcjpiZy1pbmRpZ28tNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWluZGlnby01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzTG9hZGluZyA/ICdDcmVhdGluZyBBY2NvdW50Li4uJyA6ICdDcmVhdGUgQWNjb3VudCd9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTdWJtaXQgRXJyb3IgKi99XG4gICAgICAgICAgICB7ZXJyb3JzLnN1Ym1pdCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnN1Ym1pdH1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZm9ybT5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICBBbHJlYWR5IGhhdmUgYW4gYWNjb3VudD97JyAnfVxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIvbG9naW5cIiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWluZGlnby02MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwXCI+XG4gICAgICAgICAgICAgICAgICBTaWduIGluXG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUm91dGVyIiwic3VwYWJhc2UiLCJTaWduVXBQYWdlIiwicm91dGVyIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJ1c2VybmFtZSIsImFncmVlVG9UZXJtcyIsInByb2ZpbGVQaWN0dXJlIiwic2V0UHJvZmlsZVBpY3R1cmUiLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJlbWFpbFJlZ2V4IiwidGVzdCIsImxlbmd0aCIsInVzZXJuYW1lUmVnZXgiLCJpbmNsdWRlcyIsInNpemUiLCJPYmplY3QiLCJrZXlzIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwibmFtZSIsInZhbHVlIiwidHlwZSIsImNoZWNrZWQiLCJ0YXJnZXQiLCJwcmV2IiwiaGFuZGxlRmlsZUNoYW5nZSIsImZpbGUiLCJmaWxlcyIsInVwbG9hZFByb2ZpbGVQaWN0dXJlIiwidXNlcklkIiwiZmlsZUV4dCIsInNwbGl0IiwicG9wIiwiZmlsZU5hbWUiLCJEYXRlIiwibm93IiwiZGF0YSIsImVycm9yIiwic3RvcmFnZSIsImZyb20iLCJ1cGxvYWQiLCJjb25zb2xlIiwicHVibGljVXJsIiwiZ2V0UHVibGljVXJsIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJhdXRoRGF0YSIsImF1dGhFcnJvciIsImF1dGgiLCJzaWduVXAiLCJzdWJtaXQiLCJtZXNzYWdlIiwidXNlciIsInByb2ZpbGVQaWN0dXJlVXJsIiwiaWQiLCJwcm9maWxlRXJyb3IiLCJpbnNlcnQiLCJwcm9maWxlX3BpY3R1cmVfdXJsIiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwiaDIiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImh0bWxGb3IiLCJpbnB1dCIsImF1dG9Db21wbGV0ZSIsInJlcXVpcmVkIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInAiLCJhY2NlcHQiLCJhIiwiaHJlZiIsImJ1dHRvbiIsImRpc2FibGVkIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/signup/page.tsx\n"));

/***/ })

});